// app.ts
App<IAppOption>({
  globalData: {
    userInfo: null,
    URL: "wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1",
    AKID: "LTAI5tLNjfHrqcy3EpibUPAA",
    AKKEY: "******************************",
    APPKEY: "qCuecaA4pa0dTnKP",
    WXAPPID: "wx2644979d13d1749f",
    WXAPPKEY: "65f6a64be41a958ae0f5bc069e78decf"
  },
  onLaunch() {
    // 展示本地存储能力
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
      console.log('UserInfo from cache:', JSON.stringify(userInfo));
    }

    // 登录
    wx.login({
      success: res => {
        console.log(res.code)
        // 发送 res.code 到后台换取 openId, session<PERSON>ey, unionId
        if (!this.globalData.userInfo) {
          wx.request({
            url: `https://api.weixin.qq.com/sns/jscode2session?appid=${this.globalData.WXAPPID}&secret=${this.globalData.WXAPPKEY}&js_code=${res.code}&grant_type=authorization_code`,
            success:(res)=>{
              console.log('request code res: '+JSON.stringify(res));
              this.globalData.userInfo = {
                openId: res.data.openid,
                userName: '用户'+res.data.openid,
                avatarUrl: '/assets/image/user.png'
              }
              wx.setStorageSync('userInfo', this.globalData.userInfo)
              //获取到你的openid
              console.log('userInfo: '+JSON.stringify(this.globalData.userInfo));
            }
          })
        }
      },
    })

    wx.setInnerAudioOption({
      mixWithOther: true,
      obeyMuteSwitch: false,
    })
}
})