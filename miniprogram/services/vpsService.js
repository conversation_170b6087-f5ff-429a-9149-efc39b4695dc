const HOST = 'https://unicity3dev-api.bdnrc.org.cn'
const VPS_ROAMING_API = '/xr/api/v1/ar/roaming'
const mockDataList = require('../data/index').mockMarkerDataList

const vpsRoamContent = async () => {
  return new Promise((resolve, reject) => {
    resolve(mockDataList)
    // let data = reqBody.getData();
    // wx.request({
    //   url: HOST+VPS_ROAMING_API,
    //   formData: {
    //     type: data.buffer
    //   },
    //   header: {
    //     'Content-Type': data.contentType
    //     // 如果需要 API 密钥，可以在此处添加，例如 'Authorization': 'Bearer <your-token>'
    //   },
    //   success: (res) => {
    //     if (res.data && res.data.code === 1) {
    //       console.log('vps tracking success: '+JSON.stringify(res.data))
    //       resolve(res.data.data); // 成功时返回助手回复
    //     } else {
    //       reject(new Error('未能定位成功'));
    //     }
    //   },
    //   fail: (error) => {
    //     reject(error); // 请求失败时返回错误
    //   }
    // });
  });
}

const vpsRoam = async (reqBody) => {
  console.log('vpsRoam: '+JSON.stringify(reqBody.getData()))
  return new Promise((resolve, reject) => {
    let data = reqBody.getData();
    wx.request({
      url: HOST+VPS_ROAMING_API,
      method: 'POST',
      data: data.buffer,
      header: {
        'Content-Type': data.contentType,
        'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIyOjExMjg0MDY4Mjg3NjM3NTQ0OTYiLCJyblN0ciI6IlMyVGNaaTJRTHZ3bEVFUDBNTk9iMUdzc0pGamxTT25EIiwiaWF0IjoxNzU3NDA0MjkyLCJpc3MiOiJCRE5SQy5PUkcuQ04iLCJqdGkiOiI2NmQ4ZGFmMDQ2ODc0YWQ5YWU2YzNmYzE4NjNiNTQ3OSIsInVzZXIiOiIxMTI4NDA2ODI4NzYzNzU0NDk2JDE3ODEzMjc4MzUwJDIiLCJ0eXBlIjoiVVNFUiJ9.riOtTc4Lq6tI_IWk_ymb4r6m8hXt6LKwTxCTx9mEoBc'
      },
      success: (res) => {
        if (res.data && res.data.code === 1) {
          console.log('vps tracking success: '+JSON.stringify(res.data))
          resolve(res.data.data); // 成功时返回助手回复
        } else {
          console.log('vps tracking failed: '+JSON.stringify(res.data))
          reject(new Error('未能定位成功'));
        }
      },
      fail: (error) => {
        reject(error); // 请求失败时返回错误
      }
    });
  });
}

module.exports = {
  vpsRoam,
  vpsRoamContent
}