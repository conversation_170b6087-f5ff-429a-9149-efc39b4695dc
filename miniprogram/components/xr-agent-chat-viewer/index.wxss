.chat-assistant {
  position: fixed;
  bottom: 4vh;
  width: 100%;
  height: 32vh;
  background-color: transparent;
  display: flex;
  flex-direction: column;
}

/* 输入框容器 */
.input-container {
  display: flex;
  align-items: center;
  padding: 8px;
  margin: 0 10px 10px 0;
}

/* 输入框样式，宽度占90% */
.message-input {
  flex: 1;
  width: 90%;
  height: 35px;
  padding: 0 10px;
  border: 1px solid rgb(7, 6, 6);
  border-radius: 5px;
  margin: 0 5px 0 5px;
  background: white;
}

/* 发送按钮样式 */
.send-button {
  padding: 0;
  background: transparent;
  border: none;
}

.send-button[disabled] {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
}

/* 发送按钮图标 */
.send-icon {
  width: 24px;
  height: 24px;
  padding: 12%;
  background: rgb(235, 248, 248);
  border: 1px solid rgb(235, 248, 248);
  border-radius: 50%;
}

.send-icon.disable {
  background: rgb(213, 214, 214);
  border: 1px solid rgb(213, 214, 214);
}

/* 消息窗口样式 */
.messages-window {
  flex: 1;
  padding: 10px;
  overflow-y: auto;
  width: auto;
  border: 1px solid rgb(7, 6, 6);
  border-radius: 5px;
  margin: 0 10px 20px 10px;
  background: white;
  text-align: start;
}

/* 消息项 */
.message-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

/* 头像样式 */
.avatar {
  width: 32px;
  height: 32px;
  border-radius: 25%;
  margin-right: 8px;
}

/* 用户消息 */
.user-message {
  align-self: flex-end;
  background-color: #007aff;
  color: white;
  padding: 8px;
  border-radius: 5px;
  max-width: 70%;
}

/* 助手消息 */
.assistant-message {
  align-self: flex-start;
  background-color: #e0e0e0;
  color: #333;
  padding: 8px;
  border-radius: 5px;
  max-width: 70%;
}
