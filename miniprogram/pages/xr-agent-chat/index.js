var sceneReadyBehavior = require('../behavior-scene/scene-ready');
const app = getApp()
const SpeechSynthesizer = require("../../utils/tts")
const formatTime = require("../../utils/util").formatTime
const sleep = require("../../utils/util").sleep
const getToken = require("../../utils/token").getToken
const fs = wx.getFileSystemManager()

Page({
  behaviors:[sceneReadyBehavior],
  /**
   * 页面的初始数据
   */
  data: {
    ttsStart: false, 
    ttsText: "",
    showBackBtn: true,
    ttsMessageArray: [],

    stStart : false,
    stResult : "未开始识别"
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function (options) {
      try {
          this.data.token = await getToken(app.globalData.AKID,
               app.globalData.AKKEY)
      } catch (e) {
          console.log("error on get token:", JSON.stringify(e))
          return
      }

      let tts = new SpeechSynthesizer({
          url: app.globalData.URL,
          appkey:app.globalData.APPKEY,
          token:this.data.token
      })
      
      tts.on("meta", (msg)=>{
          console.log("Client recv metainfo:", msg)
      })
  
      tts.on("data", (msg)=>{
          console.log(`recv size: ${msg.byteLength}`)
          //console.log(dumpFile.write(msg, "binary"))
          if (this.data.saveFile) {
              try {
                fs.appendFileSync(
                    this.data.saveFile,
                    msg,
                    "binary"
                )
                console.log(`append ${msg.byteLength}`)
              } catch (e) {
                console.error(e)
              }
          } else {
              console.log("save file empty")
          }
      })
  
      tts.on("completed", async (msg)=>{
        if (this.data.saveFd) {
          //console.log("Client recv completed:", msg)
          //await sleep(500)
          fs.close({
              fd : this.data.saveFd,
              success: (res)=> {
                  let ctx = wx.createInnerAudioContext()
                  ctx.autoplay = true
                  ctx.src = this.data.saveFile
                  ctx.onPlay(() => {
                      console.log('start playing..')
                  })
                  ctx.onError((res) => {
                      console.log('ctx.onError: '+res.errMsg)
                  })
                  ctx.onEnded((res)=>{
                      console.log("play done...")
                      this.cleanupSaveFile()
                  })
              },
              fail : (res)=>{
                  console.log("saved file error:" + res.errMsg)
              }
          })
        } else {
          console.warn("No file descriptor to close");
          //this.cleanupSaveFile();
        }
      })
  
      tts.on("closed", () => {
          console.log("Client recv closed")
      })
  
      tts.on("failed", (msg)=>{
          console.log("Client recv failed:", msg)
      })

      this.data.tts = tts
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  textInput: function(e) {
      this.setData({
          ttsText:e.detail.value
      })
  },
  onTtsStart: function(evt) {
    const {ttsMessage, isDone} = evt.detail
    const newMessageArray = [...this.data.ttsMessageArray, ttsMessage]
    this.setData({
      ttsMessageArray: newMessageArray
    })
    this.processTtsMessage(ttsMessage)
  },
  processTtsMessage(ttsMessage) {
    if (!ttsMessage || !this.data.tts) {
      console.log("text empty")
      wx.showToast({
          title: "文本为空",
          icon: "error",
          duration: 1000,
          mask: true
        })
      return
  }
  if (this.data.ttsStart) {
      return
  } else {
      this.setData({
        ttsStart: true
      })
  }
  console.log("try to synthesis:" + ttsMessage)
  let save = formatTime(new Date()) + ".wav"
  let savePath = wx.env.USER_DATA_PATH + "/" + save
  console.log(`save to ${savePath}`)
  fs.open({
      filePath: savePath,
      flag : "a+",
      success: async (res)=> {
          console.log(`open ${savePath} done`)
          this.setData({
            saveFile: savePath,
            saveFd: res.fd
          })
          let param = this.data.tts.defaultStartParams()
          param.text = ttsMessage
          param.voice = "zhibei_emo"
          param.speech_rate = 166
          try {
              await this.data.tts.start(param)
              console.log("tts done")
          } catch(e) {
              console.log("tts start error:" + e)
              this.cleanupAfterError()
              return
          }
      },
      fail: (res)=> {
          console.log(`open ${savePath} failed: ${res.errMsg}`)
          this.cleanupAfterError()
      }
  })
  },
  cleanupAfterError() {
    this.setData({ ttsStart: false });
    if (this.data.ttsMessageArray.length > 0) {
      const newMessageArray = this.data.ttsMessageArray.slice(1)
      this.setData({
        ttsMessageArray: newMessageArray
      }, () => this.processTtsMessage(this.data.ttsMessageArray[0]))
    }
},
  cleanupSaveFile() {
    if (this.data.saveFile) {
        fs.unlink({
            filePath: this.data.saveFile,
            success: () => {
                console.log(`Deleted file: ${this.data.saveFile}`);
                this.setData({
                  saveFile: null,
                  saveFd: null
                })
                const newMessageArray = this.data.ttsMessageArray.slice(1)
                this.setData({
                  ttsMessageArray: newMessageArray
                }, () => {
                  this.setData({ ttsStart: false }, () => {
                    if (newMessageArray.length > 0) {
                      this.processTtsMessage(newMessageArray[0])
                  }
                  });
                })
            },
            fail: (res) => {
                console.error(`Failed to delete file: ${res.errMsg}`);
            }
        });
    } else {
        console.warn("No save file to delete");
    }
},
onStStart: async function() {
  if (!this.data.st) {
      console.log("st is null")
      return
  }

  if (this.data.stStart) {
      console.log("st is started!")
      return
  }
  let st = this.data.st
  try {
      await st.start(st.defaultStartParams())
      this.data.stStart = true
  } catch (e) {
      console.log("start failed:" + e)
      return
  }

  wx.getRecorderManager().start({
      duration: 600000,
      numberOfChannels: 1,
      sampleRate : 16000,
      format: "PCM",
      frameSize: 4
  })
},

onStStop: async function() {
  wx.getRecorderManager().stop()
  //await sleep(500)
  if (this.data.stStart && this.data.st) {
      try {
          console.log("prepare close st")
          await this.data.st.close()
          this.data.stStart = false
      } catch(e) {
          console.log("close st failed:" + e)
      }
  }
}
});