var wasmLoader = require('../../../utils/wasmLoader')
let wasmExports = null;
let memoryBuffer = null;
let memBuffer = null;
let startTimeStamp

const initWasmExports = async () => {
  try {
    wasmExports = await wasmLoader.loadWasmFromAssets();
  } catch (err) {
    console.error('Failed to use Wasm:', err);
  }
}

const initMemory = (memory, totalSize) => {
  // 初始化或复用线性内存缓冲区
  if (!memBuffer || memBuffer.length < totalSize) {
    memBuffer = new Uint8Array(memory.buffer);
  }
};

const convertYuvToRgba = async (yBuffer, uvBuffer, width, height) => {
  try {
    startTimeStamp = Date.now()
    console.log('start convertYuvToRgba')
    if (!wasmExports) {
      await initWasmExports()
    }

    const memory = wasmExports.memory;
    const totalSize = yBuffer.byteLength + uvBuffer.byteLength + 4 * width * height;

    // 初始化内存
    initMemory(memory, totalSize);

    // 定义偏移位置
    const yOffset = 0;
    const uvOffset = yOffset + yBuffer.byteLength;
    const rgbaOffset = uvOffset + uvBuffer.byteLength;

    // 写入 YUV 数据到线性内存
    memBuffer.set(new Uint8Array(yBuffer), yOffset);
    memBuffer.set(new Uint8Array(uvBuffer), uvOffset);

    // 调用 Wasm 函数
    wasmExports.yuv_to_rgb(yOffset, uvOffset, rgbaOffset, width, height);

    // 获取转换后的数据（避免重复拷贝）
    const rgbaView = new Uint8Array(memBuffer.buffer, rgbaOffset, width * height * 4);

    console.log('end convertYuvToRgba: ' + (Date.now() - startTimeStamp) / 1000 + 's')
    return rgbaView;
  } catch (err) {
    console.error('Failed to use Wasm:', err);
  }
};


const drawToCanvas = (rgba, width, height) => {
  const startTimeStamp = Date.now();
  console.log('start drawToCanvas');

  // 创建离屏画布
  const canvas = wx.createOffscreenCanvas({
    type: '2d'
  });
  const ctx = canvas.getContext('2d');

  // 旋转后宽高互换
  // canvas.width = height;
  // canvas.height = width;
  canvas.width = width;
  canvas.height = height;
  
  const imageData = ctx.createImageData(width, height);
  imageData.data.set(rgba);
  
  ctx.putImageData(imageData, 0, 0);

  // // 创建临时画布并绘制原始 RGBA 数据
  // const tempCanvas = wx.createOffscreenCanvas({
  //   type: '2d'
  // });
  // const tempCtx = tempCanvas.getContext('2d');

  // // 临时画布的宽高设置为原始图像大小
  // tempCanvas.width = width;
  // tempCanvas.height = height;

  // // 创建 ImageData 并填充 RGBA 数据
  // const imageData = tempCtx.createImageData(width, height);
  // imageData.data.set(rgba);

  // // 将 RGBA 数据放入临时画布
  // tempCtx.putImageData(imageData, 0, 0);

  // 旋转并绘制到目标画布
  // ctx.translate(height, 0); // 将绘图原点移到目标位置
  // ctx.rotate(Math.PI / 2); // 顺时针旋转 90 度

  // 将图像绘制到目标画布，宽高互换
  //ctx.drawImage(tempCanvas, 0, 0);

  console.log('end drawToCanvas: ' + (Date.now() - startTimeStamp) / 1000 + 's');

  return canvas;
};


const saveBase64ToFile = (offscreenCanvas, width) => {
  return new Promise((resolve, reject) => {
    startTimeStamp = Date.now()
    const base64Image = offscreenCanvas.toDataURL('image/jpg');
    const fileManager = wx.getFileSystemManager();
    const filePath = `${wx.env.USER_DATA_PATH}/xrframe_${Date.now()}.jpg`;

    fileManager.writeFile({
      filePath,
      data: base64Image.split(',')[1], // 去掉前缀
      encoding: 'base64',
      success: () => {
        console.log('File saved successfully:', filePath);
        console.log('end saveFile: ' + (Date.now() - startTimeStamp) / 1000 + 's')
        wx.compressImage({
          compressedHeight: width,
          compressedWidth: width * 9/16,
          src: filePath,
          success: (res) => {
            console.log('cropped image saved to: ' + res.tempFilePath)
            wx.getImageInfo({
              src: res.tempFilePath,
              success: (imageInfo) => {
                console.log('cropped image size: width: ' + imageInfo.width + ', height: ' + imageInfo.height)
                // saveToAlbum(imageInfo.path)
                resolve(imageInfo)
              },
              fail: (err) => {
                console.error('Failed to get image info:', err);
                reject(err.errMsg)
              }
            })
          },
          fail: (err) => {
            console.error('Failed to crop file:', err);
            reject(err.errMsg)
          }
        })
      },
      fail: (err) => {
        console.error('Failed to save file:', err);
      },
    });
  })
}

const saveBufferToFile = (buffer) => {
  startTimeStamp = Date.now()
  const fileManager = wx.getFileSystemManager();
  const filePath = `${wx.env.USER_DATA_PATH}/xrframe_${Date.now()}.jpg`;

  fileManager.writeFile({
      filePath,
      data: buffer, // 去掉前缀
      encoding: 'binary',
      success: () => {
          console.log('File saved successfully:', filePath);
          console.log('end saveFile: '+(Date.now()-startTimeStamp)/1000+'s')
          saveToAlbum(filePath)
      },
      fail: (err) => {
          console.error('Failed to save file:', err);
      },
  });
}

const saveToAlbum = (filePath) => {
  startTimeStamp = Date.now()
  wx.saveImageToPhotosAlbum({
      filePath,
      success: () => {
          wx.showToast({ title: "Saved to album!", icon: "success" });
          console.log('end saveFile: '+(Date.now()-startTimeStamp)/1000+'s')
      },
      fail: (err) => {
          console.error("Failed to save to album:", err);
          wx.showToast({ title: "Save failed!", icon: "error" });
      },
  });
}

const yuvToImage = async (yBuffer, uvBuffer, width, height) => {
  try {
    const rgba = await convertYuvToRgba(yBuffer, uvBuffer, width, height); // YUV 转 RGB
    const canvas = drawToCanvas(rgba, width, height); // 绘制到 Canvas
    //const imageInfo = await saveBase64ToFile(canvas, width)
    const imageBase64 = canvas.toDataURL('image/jpg').split(',')[1]
    const imageBuffer = wx.base64ToArrayBuffer(imageBase64)
    //saveBufferToFile(imageBuffer)
    return imageBuffer
  } catch (err) {
    console.error("Error processing YUV data", err);
  }
}

const resizeCameraIntrinsics = (intrinsics, oldWidth, oldHeight, newWidth, newHeight) => {
  const scaleX = newWidth / oldWidth
  const scaleY = newHeight / oldHeight
  const fx = intrinsics[0] * scaleX
  const fy = intrinsics[4] * scaleY
  const cx = intrinsics[6] * scaleX
  const cy = intrinsics[7] * scaleY
  const newIntrinsics = [fx, fy, cx, cy, newWidth, newHeight]
  console.log('newIntrinsics: ' + newIntrinsics)
  return newIntrinsics
}


module.exports = {
  yuvToImage,
  saveBase64ToFile,
  saveToAlbum,
  resizeCameraIntrinsics
}