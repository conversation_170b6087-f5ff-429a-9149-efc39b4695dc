# 相机朝向检测功能说明

## 功能概述

新增了基于相机y轴朝向和marker位置的综合判断功能，用于确定相机是否准备进入marker（展项）。

## 核心功能

### `isReadyToEnterMarker(markerPos, cameraPos, cameraQuat)`

这个函数综合考虑了距离和朝向两个因素：

1. **距离检测**: 首先检查相机到marker的距离是否在阈值范围内
2. **朝向检测**: 检查相机的前向方向与marker方向的夹角是否在45度范围内

#### 参数说明
- `markerPos`: marker的位置坐标 `{x, y, z}`
- `cameraPos`: 相机的位置坐标 `{x, y, z}`
- `cameraQuat`: 相机的四元数旋转

#### 返回值
- `boolean`: `true` 表示相机准备进入marker，`false` 表示不满足条件

## 算法原理

1. **距离计算**: 使用现有的 `calculateDistance` 方法计算xz平面上的距离
2. **方向向量计算**: 计算相机到marker的方向向量（归一化）
3. **相机前向向量**: 基于相机y轴旋转角度计算前向向量
4. **角度判断**: 使用向量点积计算夹角，判断是否小于45度

## 使用场景

- 用户需要大致朝向展项才能触发进入提示
- 避免用户背对展项时误触发
- 提供更自然的交互体验

## 调试信息

函数会在控制台输出调试信息：
```
相机朝向检测 - 到marker角度: 30.5°, 阈值: 45°, 距离: 0.8m
```

## 兼容性

如果相机朝向信息不可用（`this.userPose.quaternion` 为空），系统会自动回退到仅使用距离判断的模式。

## 配置参数

- **距离阈值**: `distanceThreshold = 1` (米)
- **角度阈值**: `45度` (Math.PI/4 弧度)
- **最小距离**: `0.1米` (距离太近时直接允许进入)
